'use client';

import { useOAuthButtonState } from '@/app/hooks/useOAuthButtonState';

export default function AmazonSignIn() {
  const { isDisabled, showLoading, handleSignIn } = useOAuthButtonState('amazon', '/dashboard');

  return (
    <button
      onClick={handleSignIn}
      disabled={isDisabled}
      className="flex items-center justify-center gap-3 rounded-lg bg-white px-4 py-3 sm:px-6 sm:py-4 text-sm font-medium text-gray-700 shadow-sm border border-gray-300 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 w-full transition-colors cursor-pointer disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:bg-white"
    >
      {showLoading ? (
        <>
          <svg className="animate-spin h-5 w-5 text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
            <path className="opacity-75" fill="currentColor" d="m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          Signing in...
        </>
      ) : (
        <>
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
            {/* Amazon logo - simplified version */}
            <path
              d="M8.5 17.5c-2.5 0-4.5-2-4.5-4.5s2-4.5 4.5-4.5 4.5 2 4.5 4.5-2 4.5-4.5 4.5zm0-7.5c-1.7 0-3 1.3-3 3s1.3 3 3 3 3-1.3 3-3-1.3-3-3-3z"
              fill="#FF9900"
            />
            <path
              d="M15.5 17.5c-2.5 0-4.5-2-4.5-4.5s2-4.5 4.5-4.5 4.5 2 4.5 4.5-2 4.5-4.5 4.5zm0-7.5c-1.7 0-3 1.3-3 3s1.3 3 3 3 3-1.3 3-3-1.3-3-3-3z"
              fill="#FF9900"
            />
            <path
              d="M6 19.5c-.3 0-.5-.2-.5-.5s.2-.5.5-.5h12c.3 0 .5.2.5.5s-.2.5-.5.5H6z"
              fill="#FF9900"
            />
            <path
              d="M17 19c-.1 0-.3-.1-.4-.2-.2-.2-.2-.5 0-.7l1-1c.2-.2.5-.2.7 0s.2.5 0 .7l-1 1c-.1.1-.2.2-.3.2z"
              fill="#FF9900"
            />
          </svg>
          Amazon
        </>
      )}
    </button>
  );
}
