interface AmazonIconProps {
  size?: number;
  className?: string;
}

export default function AmazonIcon({ size = 20, className = "" }: AmazonIconProps) {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 24 24"
      className={className}
      aria-label="Amazon"
    >
      {/* Amazon logo - simplified version */}
      <path
        d="M8.5 17.5c-2.5 0-4.5-2-4.5-4.5s2-4.5 4.5-4.5 4.5 2 4.5 4.5-2 4.5-4.5 4.5zm0-7.5c-1.7 0-3 1.3-3 3s1.3 3 3 3 3-1.3 3-3-1.3-3-3-3z"
        fill="#FF9900"
      />
      <path
        d="M15.5 17.5c-2.5 0-4.5-2-4.5-4.5s2-4.5 4.5-4.5 4.5 2 4.5 4.5-2 4.5-4.5 4.5zm0-7.5c-1.7 0-3 1.3-3 3s1.3 3 3 3 3-1.3 3-3-1.3-3-3-3z"
        fill="#FF9900"
      />
      <path
        d="M6 19.5c-.3 0-.5-.2-.5-.5s.2-.5.5-.5h12c.3 0 .5.2.5.5s-.2.5-.5.5H6z"
        fill="#FF9900"
      />
      <path
        d="M17 19c-.1 0-.3-.1-.4-.2-.2-.2-.2-.5 0-.7l1-1c.2-.2.5-.2.7 0s.2.5 0 .7l-1 1c-.1.1-.2.2-.3.2z"
        fill="#FF9900"
      />
    </svg>
  );
}
